package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.mapper.ChatSessionMapper;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.service.ExportService;
import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.utils.export.*;
import com.cjx.ollama.utils.export.image.ImageUtil;
import com.cjx.ollama.utils.export.link.LinkUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_IMAGE_CATEGORY;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_POST_CATEGORY;

/**
 * 会话导出服务实现类
 * 支持TXT/HTML/XML/Excel/CSV多种格式导出，区分文章分类(postCategory)和图片分类(imageCategory)
 * author cjx
 * date 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExportServiceImpl extends ServiceImpl<ChatSessionMapper, ChatSession> implements ExportService {

    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final ChatMessageService chatMessageService;

    /**
     * 文章分类与XML标签映射（扩展支持更多分类）
     */
    public static final Map<String, String> CATEGORY_XML_MAP = Map.ofEntries(
            Map.entry("百度", "<category domain=\"category\" nicename=\"baidu\"><![CDATA[百度]]></category>"),
            Map.entry("谷歌", "<category domain=\"category\" nicename=\"google\"><![CDATA[谷歌]]></category>"),
            Map.entry("雅虎", "<category domain=\"category\" nicename=\"yahoo\"><![CDATA[雅虎]]></category>"),
            Map.entry("搜狗", "<category domain=\"category\" nicename=\"sogou\"><![CDATA[搜狗]]></category>"),
            Map.entry("未分类", "<category domain=\"category\" nicename=\"uncategorized\"><![CDATA[未分类]]></category>")
    );

    /**
     * 导出会话标题列表为TXT
     *
     * @param userId 用户ID
     * @return 会话标题字符串（换行分隔）
     */
    @Override
    public String exportSessionNameTxt(Long userId) {
        // 校验参数
        ValidationUtil.validateUserId(userId);
        log.info("导出会话标题列表，userId: {}", userId);

        List<ChatSession> sessions = this.lambdaQuery()
                .select(ChatSession::getSessionName)
                .eq(ChatSession::getUserId, userId)
                .list();

        if (sessions.isEmpty()) {
            throw new CustomerException(ResultEnum.SESSION_NOT_FOUND);
        }

        // 拼接会话名称
        String sessionNames = sessions.stream()
                .map(ChatSession::getSessionName)
                .collect(Collectors.joining("\n"));

        // 写入文件（调用工具类）
        FileWriteUtil.writeSessionNameTxt(sessionNames, sessions.size());

        LogUtil.logExportSuccess("会话标题TXT", userId, null, null, null);
        return sessionNames;
    }

    /**
     * 导出会话为HTML（含图片分类处理）
     *
     * @param userId         用户ID
     * @param sessionId      会话ID
     * @param imageCategory  图片分类（用于匹配图片资源）
     * @return HTML内容
     */
    @Override
    public String exportSessionHtml(Long userId, String sessionId, String imageCategory) {

        String processedImageCategory = StringUtils.isBlank(imageCategory) ? DEFAULT_IMAGE_CATEGORY : imageCategory;

        //参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("导出会话为HTML，sessionId: {}, imageCategory: {}", sessionId, imageCategory);

        return processExport(userId, sessionId, (session, messages) -> {
            // 生成基础HTML（调用工具类）
            String html = MarkdownUtils.generateSessionHtml(session, messages);
            // 关键词添加超链接
            String htmlWithLink = linkUtil.insertLinks(html);
            // 根据图片分类插入图片链接
            ImageUtil.ImageInjectionResult result = imageUtil.insertImageLink(
                    htmlWithLink,
                    imageLoader.getImagesByCategory(processedImageCategory)
            );
            // 写入文件（调用工具类）
            FileWriteUtil.writeSessionHtml(session, result.htmlContent());

            LogUtil.logExportSuccess("HTML", userId, sessionId, messages, null);
            return result.htmlContent();
        });
    }

    /**
     * 导出会话为WordPress XML（区分文章分类和图片分类）
     *
     * @param userId         用户ID
     * @param sessionId      会话ID
     * @param postCategory   文章分类（用于XML分类标记）
     * @param imageCategory  图片分类（用于图片资源匹配）
     * @return XML内容
     */
    @Override
    public String exportSessionXml(Long userId, String sessionId, String postCategory, String imageCategory) {
        //参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("导出会话为XML，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        String processedPostCategory = StringUtils.isBlank(postCategory) ? DEFAULT_POST_CATEGORY : postCategory;
        String processedImageCategory = StringUtils.isBlank(imageCategory) ? DEFAULT_IMAGE_CATEGORY : imageCategory;

        return processExport(userId, sessionId, (session, messages) -> {
            // 处理消息内容（含图片分类）
            StringBuilder contentBuilder = new StringBuilder();
            for (ChatMessage msg : messages) {
                // Markdown转HTML（调用工具类）
                String htmlContent = MarkdownUtils.processMessageContent(msg.getContent());
                // 添加超链接
                String withLink = linkUtil.insertLinks(htmlContent);
                // 根据图片分类插入图片
                ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                        withLink,
                        imageLoader.getImagesByCategory(processedImageCategory)
                );
                contentBuilder.append(imageResult.htmlContent()).append("\n");
            }

            // 生成XML（调用工具类）
            String xml = WordPressXmlUtil.generateWordPressXml(
                    session,
                    session.getSessionName(),
                    contentBuilder.toString(),
                    processedPostCategory
            );

            // 写入文件（调用工具类）
            FileWriteUtil.writeSessionXml(session, xml);

            LogUtil.logExportSuccess("XML", userId, sessionId, messages, null);
            return xml;
        });
    }

    /**
     * 导出会话为Excel（区分文章分类和图片分类）
     *
     * @param userId         用户ID
     * @param sessionId      会话ID
     * @param postCategory   文章分类
     * @param imageCategory  图片分类
     * @return Excel内容（Base64编码）
     */
    @Override
    public String exportSessionExcel(Long userId, String sessionId, String postCategory, String imageCategory) {
        //参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);

        log.info("导出会话为Excel，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        String processedPostCategory = StringUtils.isBlank(postCategory) ? DEFAULT_POST_CATEGORY : postCategory;
        String processedImageCategory = StringUtils.isBlank(imageCategory) ? DEFAULT_IMAGE_CATEGORY : imageCategory;

        return processExport(userId, sessionId, (session, messages) -> {
            // 生成Excel格式的文本内容
            try (StringWriter stringWriter = new StringWriter()) {
                // 写入UTF-8 BOM防止中文乱码
                stringWriter.write('\uFEFF');
                // 表头
                stringWriter.write("id\ttitle\tcontent\tcategory\timage_link\tlink\n");

                // 写入数据
                for (int i = 0; i < messages.size(); i++) {
                    ChatMessage msg = messages.get(i);
                    // 处理内容
                    String htmlContent = MarkdownUtils.toHtml(msg.getContent());
                    String htmlWithLink = linkUtil.insertLinks(htmlContent);
                    ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                            htmlWithLink,
                            imageLoader.getImagesByCategory(processedImageCategory)
                    );

                    // 构建Excel行（使用制表符分隔，类似TSV格式）
                    String line = String.format("%d\t%s\t%s\t%s\t%s\t%s%n",
                            i + 1,
                            escapeTabSeparated(session.getSessionName()),
                            escapeTabSeparated(imageResult.htmlContent()),
                            escapeTabSeparated(processedPostCategory),
                            escapeTabSeparated(imageResult.imageUrl()),
                            "" // 预留链接字段
                    );
                    stringWriter.write(line);
                }

                String excelTextContent = stringWriter.toString();

                // 写入文件（调用工具类）
                FileWriteUtil.writeSessionExcel(session, excelTextContent);

                LogUtil.logExportSuccess("Excel", userId, sessionId, messages, "文本内容");
                return excelTextContent;

            } catch (Exception e) {
                log.error("Excel生成失败", e);
                throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
            }
        });
    }

    /**
     * 导出会话为CSV（保留富文本样式，区分分类）
     *
     * @param userId         用户ID
     * @param sessionId      会话ID
     * @param postCategory   文章分类
     * @param imageCategory  图片分类
     * @return CSV内容字符串
     */
    @Override
    public String exportSessionCsv(Long userId, String sessionId, String postCategory, String imageCategory) {
        //参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);

        log.info("导出会话为CSV，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        String processedPostCategory = StringUtils.isBlank(postCategory) ? DEFAULT_POST_CATEGORY : postCategory;
        String processedImageCategory = StringUtils.isBlank(imageCategory) ? DEFAULT_IMAGE_CATEGORY : imageCategory;

        return processExport(userId, sessionId, (session, messages) -> {
            try (StringWriter stringWriter = new StringWriter()) {
                // 写入UTF-8 BOM防止中文乱码
                stringWriter.write('\uFEFF');
                // 表头
                stringWriter.write("id,title,content,category,image_link,link\n");

                // 写入数据
                for (int i = 0; i < messages.size(); i++) {
                    ChatMessage msg = messages.get(i);
                    // 处理内容
                    String htmlContent = MarkdownUtils.toHtml(msg.getContent());
                    String withLink = linkUtil.insertLinks(htmlContent);
                    ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                            withLink,
                            imageLoader.getImagesByCategory(processedImageCategory)
                    );

                    // 构建CSV行
                    String line = String.format("%d,%s,%s,%s,%s,%s%n",
                            i + 1,
                            escapeCsv(session.getSessionName()),
                            escapeCsv(imageResult.htmlContent()),
                            escapeCsv(processedPostCategory),
                            escapeCsv(imageResult.imageUrl()),
                            "" // 预留链接字段
                    );
                    stringWriter.write(line);
                }

                String csvContent = stringWriter.toString();

                // 写入文件（调用工具类）
                FileWriteUtil.writeSessionCsv(session, csvContent);

                LogUtil.logExportSuccess("CSV", userId, sessionId, messages, "CSV内容字符串");
                return csvContent;

            } catch (Exception e) {
                log.error("CSV生成失败", e);
                throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
            }
        });
    }

    /**
     * 制表符分隔内容转义处理（处理制表符、换行）
     */
    private String escapeTabSeparated(String value) {
        if (value == null) {
            return "";
        }
        // 替换制表符和换行符为空格，避免破坏TSV格式
        return value.replace("\t", " ")
                .replace("\n", " ")
                .replace("\r", " ");
    }

    /**
     * CSV内容转义处理（处理逗号、引号、换行）
     */
    private String escapeCsv(String value) {
        if (value == null) {
            return "";
        }
        // 包含特殊字符需要用引号包裹
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            // 转义双引号为两个双引号
            value = value.replace("\"", "\"\"");
            return "\"" + value + "\"";
        }
        return value;
    }



    /**
     * 封装会话和消息数据
     */
    private record SessionData(ChatSession session, List<ChatMessage> messages) {}

    /**
     * 获取验证通过的会话和消息（公共逻辑抽取）
     */
    private SessionData getValidSessionAndMessages(Long userId, String sessionId) {
        // 验证会话归属
        ChatSession session = this.lambdaQuery()
                .eq(ChatSession::getUserId, userId)
                .eq(ChatSession::getSessionId, sessionId)
                .one();
        if (session == null) {
            throw new CustomerException(ResultEnum.SESSION_NOT_FOUND);
        }

        // 获取消息列表（带权限校验）
        List<ChatMessage> messages = chatMessageService.lambdaQuery()
                .eq(ChatMessage::getSessionId, sessionId)
                .apply("EXISTS (SELECT 1 FROM chat_session s WHERE s.session_id = chat_message.session_id AND s.user_id = {0})", userId)
                .orderByAsc(ChatMessage::getCreateTime)
                .list();

        if (messages.isEmpty()) {
            throw new CustomerException(ResultEnum.MESSAGE_LIST_EMPTY);
        }

        // 过滤空内容和首条重复标题
        String sessionName = session.getSessionName();
        List<ChatMessage> filteredMessages = messages.stream()
                .filter(msg -> StringUtils.isNotBlank(msg.getContent()))
                .filter(msg -> !(messages.indexOf(msg) == 0 && msg.getContent().trim().equals(sessionName.trim())))
                .toList();

        if (filteredMessages.isEmpty()) {
            throw new CustomerException(ResultEnum.MESSAGE_LIST_EMPTY);
        }

        return new SessionData(session, filteredMessages);
    }

    /**
     * 处理会话导出的通用方法，封装会话验证和文件内容生成逻辑
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param processor 内容处理器，用于生成不同格式的文件内容
     * @return 处理器返回的结果
     */
    private <T> T processExport(Long userId, String sessionId, ContentProcessor<T> processor) {
        // 验证会话和消息（消除重复代码）
        SessionData data = getValidSessionAndMessages(userId, sessionId);
        // 调用处理器生成内容
        return processor.process(data.session(), data.messages());
    }

    /**
     * 内容处理器接口，定义不同格式文件的内容生成逻辑
     */
    @FunctionalInterface
    private interface ContentProcessor<T> {
        T process(ChatSession session, List<ChatMessage> messages);
    }
}
