package com.cjx.ollama.utils.export;

import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.result.ResultEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static com.cjx.ollama.utils.constant.Export.*;

/**
 * 文件写入工具类
 * 封装各类文件（TXT、HTML、XML 等）的写入逻辑
 */
@Slf4j
public class FileWriteUtil {
    private FileWriteUtil() {
        throw new UnsupportedOperationException("FileWriteUtil工具类不应该被实例化");
    }

    /**
     * 写入会话标题 TXT 文件
     * @param content  文件内容
     * @param count    记录数量
     * @throws CustomerException 自定义异常
     */
    public static void writeSessionNameTxt(String content, int count) {
        try {
            // 准备目录
            prepareDirectory(SESSION_NAME_TXT_LOCATION, "TXT");

            String fileName = "会话标题列表_" + System.currentTimeMillis() + ".txt";
            String filePath = Paths.get(SESSION_NAME_TXT_LOCATION, fileName).toString();

            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write(content);
            }

            log.info("TXT 文件写入成功，路径: {}, 记录数: {}", filePath, count);
        } catch (IOException e) {
            handleFileWriteError("TXT", e);
        }
    }

    /**
     * 写入 HTML 文件
     * @param session  会话信息
     * @param html     HTML 内容
     * @throws CustomerException 自定义异常
     */
    public static void writeSessionHtml(ChatSession session, String html) {
        String fileName = session.getSessionName() + "_" + System.currentTimeMillis() + ".html";
        writeTextFile(SESSION_HTML_LOCATION, fileName, html, "HTML");
    }

    /**
     * 写入 XML 文件
     * @param session  会话信息
     * @param xml      XML 内容
     * @throws CustomerException 自定义异常
     */
    public static void writeSessionXml(ChatSession session, String xml) {
        String fileName = session.getSessionName() + "_" + System.currentTimeMillis() + ".xml";
        writeTextFile(SESSION_XML_LOCATION, fileName, xml, "XML");
    }

    /**
     * 写入 Excel 文件（优化后：直接实现逻辑，避免固定参数传递）
     * @param session     会话信息
     * @param excelBytes  Excel 文件字节数组
     * @throws CustomerException 自定义异常
     */
    public static void writeSessionExcel(ChatSession session, byte[] excelBytes) {
        try {
            // 直接使用固定的目录和文件类型，无需通过参数传递
            String fileType = "Excel";
            prepareDirectory(SESSION_EXCEL_LOCATION, fileType);

            String fileName = session.getSessionName() + "_" + System.currentTimeMillis() + ".xlsx";
            String filePath = Paths.get(SESSION_EXCEL_LOCATION, fileName).toString();

            Files.write(Paths.get(filePath), excelBytes);

        } catch (Exception e) {
            handleFileWriteError("Excel", e);
        }
    }

    /**
     * 写入 CSV 文件
     * @param session  会话信息
     * @param csv      CSV 内容
     * @throws CustomerException 自定义异常
     */
    public static void writeSessionCsv(ChatSession session, String csv) {
        String fileName = session.getSessionName() + "_" + System.currentTimeMillis() + ".csv";
        writeTextFile(SESSION_CSV_LOCATION, fileName, csv, "CSV");
    }

    /**
     * 通用文本文件写入方法
     * @param dirPath  目录路径
     * @param fileName 文件名
     * @param content  文件内容
     * @param fileType 文件类型（用于日志）
     * @throws CustomerException 自定义异常
     */
    private static void writeTextFile(String dirPath, String fileName, String content, String fileType) {
        try {
            prepareDirectory(dirPath, fileType);
            String filePath = Paths.get(dirPath, fileName).toString();
            Files.writeString(Paths.get(filePath), content);
        } catch (Exception e) {
            handleFileWriteError(fileType, e);
        }
    }

    /**
     * 准备目录（创建不存在的目录）
     * @param dirPath 目录路径
     * @param fileType 文件类型（用于日志）
     */
    private static void prepareDirectory(String dirPath, String fileType) {
        File dir = new File(dirPath);
        if (!dir.exists() &&!dir.mkdirs()) {
            log.error("创建 {} 目录失败: {}", fileType, dir.getAbsolutePath());
            throw new CustomerException(ResultEnum.DIRECTORY_CREATE_FAILED);
        }
    }

    /**
     * 处理文件写入错误
     * @param fileType 文件类型
     * @param e 异常对象
     */
    private static void handleFileWriteError(String fileType, Exception e) {
        log.error("{} 写入失败", fileType, e);
        throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
    }
}